name: Build and Deploy Frontend to GCS

on:
  push:
    branches: [develop]

jobs:
  build-and-deploy-frontend:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./frontend

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Enable Corepack
        run: corepack enable

      - name: Setup Yarn
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "yarn"
          cache-dependency-path: "./frontend/yarn.lock"

      - name: Install dependencies
        run: yarn install --frozen-lockfile --prefer-offline

      - name: Build project
        run: yarn dev-build

      - name: Setup gcloud CLI
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Install gcloud CLI components
        run: |
          sudo apt-get install google-cloud-cli

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: game-460004

      - name: Deploy to Google Cloud Storage
        run: |
          gsutil -m rsync -r -d ./dist gs://game-460004-frontend-dev
